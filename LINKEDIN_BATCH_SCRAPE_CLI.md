# LinkedIn 批量爬取命令行工具

## 🎯 功能概述

这个命令行工具用于批量处理LinkedIn数据爬取任务，具有以下特性：

- **批量处理**: 每次从数据库获取1000条未处理的记录
- **限流控制**: 严格遵守每分钟30次的API调用限制
- **循环处理**: 自动循环处理直到所有数据处理完成
- **详细日志**: 提供详细的处理进度和统计信息
- **错误处理**: 完善的异常处理和错误统计

## 🚀 快速开始

### 1. 编译项目
```bash
mvn clean package -DskipTests
```

### 2. 运行命令行工具

#### 自动模式（推荐用于生产环境）
```bash
java -jar target/worker-*.jar --spring.profiles.active=prod ai.beyz.worker.cli.LinkedinScrapeCliApplication auto
```

#### 交互模式（推荐用于开发和测试）
```bash
java -jar target/worker-*.jar --spring.profiles.active=prod ai.beyz.worker.cli.LinkedinScrapeCliApplication
```

## 📋 使用说明

### 交互模式命令

在交互模式下，你可以使用以下命令：

1. **start** - 开始批量处理
2. **status** - 显示当前状态和配置
3. **help** - 显示帮助信息
4. **exit** - 退出程序

### 自动模式

在自动模式下，程序会立即开始处理，无需用户交互。

## 🔧 工作流程

1. **数据获取**: 调用 `SendDataToQueueService.findUnscrapedWithEmailAndLinkedinUrl(1000)` 获取未处理的数据
2. **数据解析**: 从消息中提取 email 和 LinkedIn URL
3. **限流处理**: 使用 RateLimiter 确保每分钟最多30次调用
4. **数据处理**: 调用 `LinkedinScrapeServiceV2.workflow(email, url)` 处理每条记录
5. **循环执行**: 重复上述步骤直到没有更多数据

## 📊 处理统计

程序会实时显示以下统计信息：

- **批次信息**: 当前处理的批次号和记录数
- **成功/失败统计**: 成功处理和失败的记录数量
- **处理速度**: 平均每条记录的处理时间
- **总体进度**: 总处理时间和处理速率

## 🌐 Web API 接口

除了命令行工具，还提供了Web API接口：

### 启动批处理
```bash
POST /api/v1/batch/linkedin-scrape/start
```

### 查看状态
```bash
GET /api/v1/batch/linkedin-scrape/status
```

### 停止处理
```bash
POST /api/v1/batch/linkedin-scrape/stop
```

### 查看配置
```bash
GET /api/v1/batch/linkedin-scrape/config
```

### 健康检查
```bash
GET /api/v1/batch/linkedin-scrape/health
```

## ⚙️ 配置说明

### 限流配置
- **频率**: 每分钟30次 (0.5次/秒)
- **实现**: 使用Google Guava RateLimiter
- **配置文件**: `RateLimiterConfig.java`

### 批处理配置
- **批次大小**: 1000条记录
- **数据源**: `mailingList` 表中 `scraped=false` 的记录
- **处理方法**: `LinkedinScrapeServiceV2.workflow`

## 📝 日志示例

```
🚀 Starting batch LinkedIn scraping process...
📦 Processing batch #1 (fetching 1000 records)...
📋 Found 1000 records in batch #1
🔄 Processing item 1/1000 - Email: <EMAIL>, URL: https://linkedin.com/in/user
✅ Successfully processed: <EMAIL> -> talent-12345
📊 Progress: 10/1000 items processed in batch #1 (avg: 2150.50ms/item)
...
🏁 Batch LinkedIn scraping completed!
📊 Final Statistics:
   - Total Batches: 5
   - Total Processed: 4500
   - Total Success: 4200
   - Total Failed: 300
   - Success Rate: 93.33%
   - Total Time: 180.50 seconds
   - Average Time per Item: 2150.50ms
   - Processing Rate: 25.00 items/minute
```

## ⚠️ 注意事项

1. **数据库连接**: 确保数据库连接配置正确
2. **API密钥**: 确保LinkedIn API密钥有效且有足够配额
3. **网络稳定**: 处理过程中需要稳定的网络连接
4. **磁盘空间**: 确保有足够的磁盘空间存储日志
5. **内存使用**: 大批量处理时注意内存使用情况

## 🛠️ 故障排查

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置和网络连接
   - 确认数据库服务正在运行

2. **API调用失败**
   - 检查API密钥是否有效
   - 确认API配额是否充足
   - 检查网络连接

3. **内存不足**
   - 增加JVM堆内存: `-Xmx2g`
   - 减少批处理大小

4. **处理速度慢**
   - 检查网络延迟
   - 确认数据库性能
   - 查看API响应时间

5. **Unicode字符错误**
   - 错误信息: `unsupported Unicode escape sequence \u0000`
   - 原因: LinkedIn数据中包含PostgreSQL不支持的null字符
   - 解决方案: 程序已自动清理无效字符，如仍有问题请检查数据源

### 日志级别

可以通过以下方式调整日志级别：

```bash
java -jar app.jar --logging.level.ai.beyz=DEBUG
```

## 🔍 监控建议

1. **处理进度**: 定期检查日志中的进度信息
2. **错误率**: 监控失败率，如果过高需要调查原因
3. **处理速度**: 监控处理速度，确保在预期范围内
4. **资源使用**: 监控CPU、内存和网络使用情况

## 📞 技术支持

如果遇到问题，请：

1. 检查日志文件中的详细错误信息
2. 确认配置文件设置正确
3. 验证数据库和API连接
4. 联系技术支持团队

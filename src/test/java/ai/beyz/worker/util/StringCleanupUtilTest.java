package ai.beyz.worker.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class StringCleanupUtilTest {

    @Test
    void testCleanString_NullInput() {
        assertNull(StringCleanupUtil.cleanString(null));
    }

    @Test
    void testCleanString_EmptyString() {
        assertEquals("", StringCleanupUtil.cleanString(""));
    }

    @Test
    void testCleanString_NormalString() {
        String input = "Hello World";
        assertEquals(input, StringCleanupUtil.cleanString(input));
    }

    @Test
    void testCleanString_WithNullCharacter() {
        String input = "Hello\u0000World";
        String expected = "HelloWorld";
        assertEquals(expected, StringCleanupUtil.cleanString(input));
    }

    @Test
    void testCleanString_WithControlCharacters() {
        String input = "Hello\u0001\u0002World\u0003";
        String expected = "HelloWorld";
        assertEquals(expected, StringCleanupUtil.cleanString(input));
    }

    @Test
    void testCleanString_PreserveValidWhitespace() {
        String input = "Hello\nWorld\tTest\r";
        assertEquals(input, StringCleanupUtil.cleanString(input));
    }

    @Test
    void testCleanString_WithBOM() {
        String input = "\uFEFFHello World";
        String expected = "Hello World";
        assertEquals(expected, StringCleanupUtil.cleanString(input));
    }

    @Test
    void testCleanString_WithZeroWidthChars() {
        String input = "Hello\u200BWorld\u200C\u200D\u2060";
        String expected = "HelloWorld";
        assertEquals(expected, StringCleanupUtil.cleanString(input));
    }

    @Test
    void testCleanJsonString_NullInput() {
        assertNull(StringCleanupUtil.cleanJsonString(null));
    }

    @Test
    void testCleanJsonString_WithNewlines() {
        String input = "Hello\nWorld\tTest\r";
        String expected = "Hello\\nWorld\\tTest\\r";
        assertEquals(expected, StringCleanupUtil.cleanJsonString(input));
    }

    @Test
    void testCleanJsonString_WithNullCharacter() {
        String input = "{\"text\":\"Hello\u0000World\"}";
        String expected = "{\"text\":\"HelloWorld\"}";
        assertEquals(expected, StringCleanupUtil.cleanJsonString(input));
    }

    @Test
    void testContainsInvalidChars_NullInput() {
        assertFalse(StringCleanupUtil.containsInvalidChars(null));
    }

    @Test
    void testContainsInvalidChars_CleanString() {
        assertFalse(StringCleanupUtil.containsInvalidChars("Hello World"));
    }

    @Test
    void testContainsInvalidChars_WithNullChar() {
        assertTrue(StringCleanupUtil.containsInvalidChars("Hello\u0000World"));
    }

    @Test
    void testContainsInvalidChars_WithControlChar() {
        assertTrue(StringCleanupUtil.containsInvalidChars("Hello\u0001World"));
    }

    @Test
    void testContainsInvalidChars_WithBOM() {
        assertTrue(StringCleanupUtil.containsInvalidChars("\uFEFFHello World"));
    }

    @Test
    void testGetInvalidCharStats_NullInput() {
        StringCleanupUtil.InvalidCharStats stats = StringCleanupUtil.getInvalidCharStats(null);
        assertEquals(0, stats.getTotalInvalidChars());
    }

    @Test
    void testGetInvalidCharStats_CleanString() {
        StringCleanupUtil.InvalidCharStats stats = StringCleanupUtil.getInvalidCharStats("Hello World");
        assertEquals(0, stats.getTotalInvalidChars());
    }

    @Test
    void testGetInvalidCharStats_WithInvalidChars() {
        String input = "Hello\u0000World\u0001\uFEFF\u200B";
        StringCleanupUtil.InvalidCharStats stats = StringCleanupUtil.getInvalidCharStats(input);
        assertTrue(stats.getTotalInvalidChars() > 0);
    }

    @Test
    void testSafeTruncate_NullInput() {
        assertNull(StringCleanupUtil.safeTruncate(null, 10));
    }

    @Test
    void testSafeTruncate_ShorterThanLimit() {
        String input = "Hello";
        assertEquals(input, StringCleanupUtil.safeTruncate(input, 10));
    }

    @Test
    void testSafeTruncate_ExactLimit() {
        String input = "HelloWorld";
        assertEquals(input, StringCleanupUtil.safeTruncate(input, 10));
    }

    @Test
    void testSafeTruncate_LongerThanLimit() {
        String input = "HelloWorldTest";
        String result = StringCleanupUtil.safeTruncate(input, 10);
        assertEquals(10, result.length());
        assertEquals("HelloWorld", result);
    }

    @Test
    void testSafeTruncate_WithInvalidChars() {
        String input = "Hello\u0000World\u0001Test";
        String result = StringCleanupUtil.safeTruncate(input, 10);
        assertEquals("HelloWorld", result); // Invalid chars removed, then truncated
    }

    @Test
    void testSafeTruncate_WithSurrogateChars() {
        // Test with emoji (surrogate pair)
        String input = "Hello👋World";
        String result = StringCleanupUtil.safeTruncate(input, 7);
        // Should not break the emoji
        assertTrue(result.length() <= 7);
        assertFalse(result.endsWith("👋")); // Should not end with broken surrogate
    }

    @Test
    void testRealWorldExample_LinkedInData() {
        // Simulate the actual error case
        String input = "...tions and provided information to promote e\u0000ffective communication...";
        String cleaned = StringCleanupUtil.cleanString(input);
        assertFalse(cleaned.contains("\u0000"));
        assertEquals("...tions and provided information to promote effective communication...", cleaned);
    }

    @Test
    void testJsonExample_WithComplexData() {
        String jsonInput = "{\"description\":\"Hello\u0000World\nNew line\tTab\"}";
        String cleaned = StringCleanupUtil.cleanJsonString(jsonInput);
        
        assertFalse(cleaned.contains("\u0000"));
        assertTrue(cleaned.contains("\\n")); // Newline should be escaped
        assertTrue(cleaned.contains("\\t")); // Tab should be escaped
        assertEquals("{\"description\":\"HelloWorld\\nNew line\\tTab\"}", cleaned);
    }

    @Test
    void testInvalidCharStats_ToString() {
        StringCleanupUtil.InvalidCharStats stats = new StringCleanupUtil.InvalidCharStats(1, 2, 1, 1);
        String result = stats.toString();
        assertTrue(result.contains("null=1"));
        assertTrue(result.contains("control=2"));
        assertTrue(result.contains("bom=1"));
        assertTrue(result.contains("zeroWidth=1"));
        assertTrue(result.contains("total=5"));
    }
}

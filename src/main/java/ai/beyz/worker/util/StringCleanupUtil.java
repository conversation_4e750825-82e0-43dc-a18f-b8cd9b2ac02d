package ai.beyz.worker.util;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Pattern;

/**
 * 字符串清理工具类
 * 用于清理数据中的无效字符，特别是PostgreSQL不支持的Unicode字符
 */
@Slf4j
public class StringCleanupUtil {

    // 匹配所有控制字符的正则表达式，但保留常用的换行符、制表符等
    private static final Pattern CONTROL_CHARS_PATTERN = Pattern.compile("[\\p{Cntrl}&&[^\r\n\t]]");
    
    // 匹配null字符的正则表达式
    private static final Pattern NULL_CHAR_PATTERN = Pattern.compile("\u0000");

    /**
     * 清理字符串中的无效字符
     * 
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    public static String cleanString(String input) {
        if (input == null) {
            return null;
        }
        
        String original = input;
        
        // 1. 移除null字符 (\u0000)
        String cleaned = NULL_CHAR_PATTERN.matcher(input).replaceAll("");
        
        // 2. 移除其他控制字符，但保留换行符、回车符和制表符
        cleaned = CONTROL_CHARS_PATTERN.matcher(cleaned).replaceAll("");
        
        // 3. 清理其他可能有问题的字符
        cleaned = cleaned
                .replace("\uFEFF", "") // 移除BOM字符
                .replace("\u200B", "") // 移除零宽空格
                .replace("\u200C", "") // 移除零宽非连接符
                .replace("\u200D", "") // 移除零宽连接符
                .replace("\u2060", ""); // 移除字符连接抑制符
        
        // 记录清理操作
        if (!cleaned.equals(original)) {
            log.debug("🧹 Cleaned string: original length={}, cleaned length={}, removed {} characters", 
                    original.length(), cleaned.length(), original.length() - cleaned.length());
        }
        
        return cleaned;
    }

    /**
     * 清理JSON字符串中的无效字符
     * 
     * @param jsonString JSON字符串
     * @return 清理后的JSON字符串
     */
    public static String cleanJsonString(String jsonString) {
        if (jsonString == null) {
            return null;
        }
        
        String original = jsonString;
        
        // 先进行基本的字符串清理
        String cleaned = cleanString(jsonString);
        
        // JSON特定的清理：确保换行符和制表符被正确转义
        cleaned = cleaned
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
        
        // 记录清理操作
        if (!cleaned.equals(original)) {
            log.warn("⚠️ Cleaned invalid characters from JSON string. Original length: {}, Cleaned length: {}", 
                    original.length(), cleaned.length());
        }
        
        return cleaned;
    }

    /**
     * 检查字符串是否包含无效字符
     * 
     * @param input 输入字符串
     * @return 如果包含无效字符返回true
     */
    public static boolean containsInvalidChars(String input) {
        if (input == null) {
            return false;
        }
        
        return NULL_CHAR_PATTERN.matcher(input).find() || 
               CONTROL_CHARS_PATTERN.matcher(input).find() ||
               input.contains("\uFEFF") ||
               input.contains("\u200B") ||
               input.contains("\u200C") ||
               input.contains("\u200D") ||
               input.contains("\u2060");
    }

    /**
     * 获取字符串中无效字符的统计信息
     * 
     * @param input 输入字符串
     * @return 无效字符统计信息
     */
    public static InvalidCharStats getInvalidCharStats(String input) {
        if (input == null) {
            return new InvalidCharStats(0, 0, 0, 0);
        }
        
        int nullChars = NULL_CHAR_PATTERN.matcher(input).replaceAll("").length() - input.length();
        int controlChars = CONTROL_CHARS_PATTERN.matcher(input).replaceAll("").length() - input.length();
        int bomChars = (int) input.chars().filter(ch -> ch == 0xFEFF).count();
        int zeroWidthChars = (int) input.chars().filter(ch -> 
                ch == 0x200B || ch == 0x200C || ch == 0x200D || ch == 0x2060).count();
        
        return new InvalidCharStats(Math.abs(nullChars), Math.abs(controlChars), bomChars, zeroWidthChars);
    }

    /**
     * 无效字符统计信息
     */
    public static class InvalidCharStats {
        private final int nullChars;
        private final int controlChars;
        private final int bomChars;
        private final int zeroWidthChars;

        public InvalidCharStats(int nullChars, int controlChars, int bomChars, int zeroWidthChars) {
            this.nullChars = nullChars;
            this.controlChars = controlChars;
            this.bomChars = bomChars;
            this.zeroWidthChars = zeroWidthChars;
        }

        public int getNullChars() { return nullChars; }
        public int getControlChars() { return controlChars; }
        public int getBomChars() { return bomChars; }
        public int getZeroWidthChars() { return zeroWidthChars; }
        public int getTotalInvalidChars() { return nullChars + controlChars + bomChars + zeroWidthChars; }

        @Override
        public String toString() {
            return String.format("InvalidCharStats{null=%d, control=%d, bom=%d, zeroWidth=%d, total=%d}", 
                    nullChars, controlChars, bomChars, zeroWidthChars, getTotalInvalidChars());
        }
    }

    /**
     * 安全地截断字符串到指定长度，避免在多字节字符中间截断
     * 
     * @param input 输入字符串
     * @param maxLength 最大长度
     * @return 截断后的字符串
     */
    public static String safeTruncate(String input, int maxLength) {
        if (input == null || input.length() <= maxLength) {
            return input;
        }
        
        // 先清理无效字符
        String cleaned = cleanString(input);
        
        if (cleaned.length() <= maxLength) {
            return cleaned;
        }
        
        // 截断到指定长度，但确保不在多字节字符中间截断
        String truncated = cleaned.substring(0, maxLength);
        
        // 检查最后一个字符是否是多字节字符的一部分
        if (Character.isHighSurrogate(truncated.charAt(truncated.length() - 1))) {
            // 如果是高代理项，移除它以避免无效的字符
            truncated = truncated.substring(0, truncated.length() - 1);
        }
        
        return truncated;
    }
}
